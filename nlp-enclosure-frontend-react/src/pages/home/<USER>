/* 主页面样式 */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: #fff;
  padding: 0;
  border-bottom: 1px solid #f0f0f0;
}

.sider {
  background: #fff;
  border-right: 1px solid #f0f0f0;
}

.content {
  background: #fff;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

/* 消息列表样式 */
.messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.messagesExpanded {
  /* 有消息时的样式 */
}

.messagesCollapsed {
  /* 无消息时的样式 */
}

/* 输入框样式 */
.sender {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 占位符样式 */
.placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

/* 建议提示样式 */
.suggestionPrompts {
  cursor: pointer;
  transition: all 0.2s;
}

.suggestionPrompts:hover {
  background-color: #f5f5f5;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .sider {
    display: none;
  }
  
  .content {
    margin-left: 0;
  }
}

/* 其他通用样式 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.error {
  color: #ff4d4f;
  text-align: center;
  padding: 20px;
}

.success {
  color: #52c41a;
  text-align: center;
  padding: 20px;
}
