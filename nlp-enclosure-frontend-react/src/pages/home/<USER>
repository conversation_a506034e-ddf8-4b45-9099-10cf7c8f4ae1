import { MenuProps } from 'antd';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  PushpinOutlined
} from '@ant-design/icons';

export const llmItems: MenuProps['items'] = [
  {
    key: 'Qwen2.5:32B-instruct',
    label: (
        <div className="llm-dropdown">
          <span className="title">Qwen2.5:32B-instruct</span>
          <span className="desc">多场景智能小能手</span>
        </div>
    )
  },
  // {
  //   key: 'QwQ:32B',
  //   label: (
  //       <div className="llm-dropdown">
  //         <span className="title">QwQ:32B</span>
  //         <span className="desc">小体量强推理担当</span>
  //       </div>
  //   )
  // },
  {
    key: 'Qwen2.5-VL:32B-instruct',
    label: (
        <div className="llm-dropdown">
          <span className="title">Qwen2.5-VL:32B-instruct</span>
          <span className="desc">图像识别智能小能手</span>
        </div>
    )
  },
  {
    key: 'DeepSeek-R1:671B',
    label: (
        <div className="llm-dropdown">
          <span className="title">DeepSeek-R1:671B</span>
          <span className="desc">千亿参数推理王者</span>
        </div>
    )
  },
];

export const agentCardMoreItems: MenuProps['items'] = [
  {
    key: 'addToList',
    label: '添加至列表',
    icon: <PlusOutlined />
  },
  {
    key: 'visibility',
    label: '设置可见性',
    icon: <EyeOutlined />
  },
  {
    key: 'edit',
    label: '编辑',
    icon: <EditOutlined />
  },
  {
    key: 'remove',
    label: '删除',
    icon: <DeleteOutlined />,
    danger: true
  }
];

export const userNodeDropdown: MenuProps['items'] = [
  {
    key: '1',
    label: '登出'
  }
];

export const getAgentListMoreItems = (userAgent: any): MenuProps['items'] => {
  return [
    {
      key: 'top',
      label: userAgent.isTop ? '取消固定' : '固定',
      icon: <PushpinOutlined />
    },
    {
      key: 'remove',
      label: '删除',
      icon: <DeleteOutlined />,
      danger: true
    }
  ];
};
