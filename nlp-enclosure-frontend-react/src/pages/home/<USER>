import {
  Attachments,
  Bubble,
  BubbleProps,
  Conversations,
  ConversationsProps,
  PromptProps,
  Prompts,
  Sender,
  useXAgent,
  useXChat,
  Welcome,
  XStream
} from '@ant-design/x';
import { Conversation } from '@ant-design/x/es/conversations';
import markdownit from 'markdown-it';
import React, {useEffect, useRef, useState} from 'react';
import { useHomeStyles } from './styles';
import { llmItems, agentCardMoreItems, getAgentListMoreItems } from './constants';
import WelcomePlaceholder from './components/WelcomePlaceholder';
import LogoSection from './components/LogoSection';
import NewConversationButton from './components/NewConversationButton';
import UserSection from './components/UserSection';
import HistoricalDialogues from './components/HistoricalDialogues';
import AttachmentsUpload from './components/AttachmentsUpload';
import AgentList from './components/AgentList';
import ChatSidebar from './components/ChatSidebar';
import ChatContent from './components/ChatContent';
import MessageRenderer from './components/MessageRenderer';
import SenderFooter from './components/SenderFooter';
import { MenuToggleButton, LLMSelectorButton, CreateAgentButton } from './components/buttons';

// Hooks
import { useAudioPlayer } from './hooks/useAudioPlayer';
import { useAgentManager } from './hooks/useAgentManager';
import { useChatManager } from './hooks/useChatManager';
import { useFileUpload } from './hooks/useFileUpload';
import { useUIState } from './hooks/useUIState';
import { useModalState } from './hooks/useModalState';
import { useConversationManager } from './hooks/useConversationManager';
import { useAgentOperations } from './hooks/useAgentOperations';

import Icon, {
  AlignLeftOutlined,
  ArrowRightOutlined,
  CloudUploadOutlined,
  CopyOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  MoreOutlined,
  FireOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PaperClipOutlined,
  PlusOutlined,
  PushpinOutlined,
  ReadOutlined,
  LikeOutlined,
  DislikeOutlined,
  LikeFilled,
  DislikeFilled,
  FileAddOutlined, EyeOutlined,
  SoundOutlined, ReloadOutlined
} from '@ant-design/icons';
import { MessageInfo } from '@ant-design/x/es/use-x-chat';
import {
  message as antdMessage,
  Avatar,
  Badge,
  Button,
  ConfigProvider,
  Divider,
  Dropdown,
  type GetProp,
  Image,
  Input,
  Layout,
  Menu,
  MenuProps,
  Modal,
  Popover,
  Space,
  Spin,
  Tooltip,
  Typography,
  Upload,
  Drawer,
  Flex,
  Form,
  Switch,
  Radio
} from 'antd';
import SubMenu from 'antd/es/menu/SubMenu';
import { RcFile } from 'antd/es/upload/interface';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { findByIsDefaultFalse, getAgentDictByIsDefaultTrue, deleteAgentDict, findAllVisible } from '../../services/agentDict.ts';
import {
  message as getAgentMessage,
  parameters,
  stop,
  streamApi,
  suggested,
  upload,
  feedback,
  closeAllEventSources
} from '../../services/difyApi.ts';
import PlayButton from '../../components/PlayButton';
import {deleteChatHistory, getChatHistoryList, renameChatHistory, topChatHistory} from "../../services/chatHistory.ts";
import { isSuperSonicAgent, createSuperSonicAgentParam } from '../../services/supersonic';
import SuperSonicChatWrapper from '../../components/SuperSonicChatWrapper';
import { getUserInfo, logout } from '../../services/user.ts';
import { Account } from '../../types/account.ts';
import {AgentDict, getColorPairByBgColor} from '../../types/agentDict.ts';
import { AgentParam } from '../../types/agentParam.ts';
import { DifyFile } from '../../types/difyFile.ts';
import { AgentMessage, RetrieverResource, StreamResponse } from '../../types/streamResponse.ts';
import welcomeIcon from './img/fmt.webp';
import agentSvg from './img/agent.svg'
import logoIcon from './img/logo.png';
import { ReactComponent as QwenThinkIcon } from './img/qwenThink.svg';
import './style.css';
// import audioPlayer, { AudioPlayerEventCallback, AudioPlayerEventType, AudioPlayerState } from '../../services/audioPlayerService';
import audioPlayer, { AudioPlayerEventCallback, AudioPlayerEventType, AudioPlayerState } from '../../services/howlerAudioPlayerService';


import { useIsMobile } from '../../utils/device.ts';
import AgentFormModal from "../../components/agentManager/agentFormModal.tsx";
import {buttonTheme} from "../../theme.tsx";
import {addUserAgent, deleteUserAgent, getUserAgentList, setTopUserAgent} from "../../services/userAgentList.ts";
import {UserAgentList} from "../../types/userAgentList.ts";
import FloatingMenu from '../../components/FloatingMenu';
import VisibilitySelector from '../../components/AgentDictVisibility';
import { getWWRegisterData, tryRegisterWW } from '../../services/wwService.ts';
import {
  initMicrophoneService,
  startRecording,
  stopRecording,
  translateVoice
} from '../../services/microphoneService.ts';
import {getBaseUrlWithoutProtocol, initSTTWebSocket, initTTSWebSocket} from '../../services/websocketService.ts';


const { Header, Sider, Content } = Layout;

const md = markdownit({ html: true, breaks: true });

const Independent: React.FC = () => {
  // ==================== Style ====================
  const { styles } = useHomeStyles();

  // ==================== Hooks ====================
  const isMobile = useIsMobile();

  // 使用自定义Hook
  const audioPlayerHook = useAudioPlayer();
  const agentManagerHook = useAgentManager();
  const chatManagerHook = useChatManager();
  const fileUploadHook = useFileUpload();

  // 使用新的状态管理Hook
  const uiState = useUIState();
  const modalState = useModalState();
  const conversationManager = useConversationManager();
  const agentOperations = useAgentOperations();

  // ==================== State ====================
  const [content, setContent] = React.useState('');

  const [attachedFiles, setAttachedFiles] = React.useState<GetProp<typeof Attachments, 'items'>>(
    []
  );

  const [userItem, setUserItem] = React.useState<Account | null>(null);

  const [llmDefaultSelect, setLlmDefaultSelect] = React.useState(localStorage.getItem('llmDefaultSelect') || 'Qwen2.5:32B-instruct');

  const [messageFeedBackDict, setMessageFeedBackDict] = React.useState<{
    [key: string]: string | null;
  }>({});

  const [isSpeechRecording, setIsSpeechRecording] = React.useState(false);

  // 从Hook中解构状态和方法
  const {
    isShowSpin, setIsShowSpin,
    headerOpen, setHeaderOpen,
    collapsed, setCollapsed,
    drawerOpen, setDrawerOpen,
    isLogoSpanVisible,
    contentShowState, setContentShowState,
    activeKey, setActiveKey,
    nodeMenuSelectedKey, setNodeMenuSelectedKey,
    selectedAgentId, setSelectedAgentId
  } = uiState;

  const {
    isShowRenameModal, setIsShowRenameModal,
    renameModalType, setRenameModalType,
    isOpenAgentFormModal, setIsOpenAgentFormModal,
    isShowVisibleSelectorDialog, setIsShowVisibleSelectorDialog,
    visibleSelectAgentDictId, setVisibleSelectAgentDictId,
    beRenameConversionId, setBeRenameConversionId,
    chatNameForm
  } = modalState;

  const {
    conversationsItems, setConversationsItems,
    isBeRefreshConversations, setIsBeRefreshConversations,
    isBeRefreshConversationsRef,
    beLoadHistoryMessageConversationId, setBeLoadHistoryMessageConversationId,
    loadConversation, loadHistoryMessage
  } = conversationManager;

  const {
    agentList, setAgentList,
    currentUserAgentList, setCurrentUserAgentList,
    currentAgent, setCurrentAgent,
    currentAgentParam, setCurrentAgentParam,
    defaultAgent, setDefaultAgent,
    defaultAgentIsOpenThink, setDefaultAgentIsOpenThink,
    isDisableSwitchOpenThink, setIsDisableSwitchOpenThink,
    currentTaskId, setCurrentTaskId,
    currentSuperSonicAgentId, setCurrentSuperSonicAgentId,
    isCanCreateAgent, setIsCanCreateAgent,
    agentFormData, setAgentFormData,
    suggestedPrompts, setSuggestedPrompts,
    loadAgentParameters, initializeMessage, fetchAgentList, fetchDefaultAgent,
    updateDefaultAgentStateByMessage
  } = agentOperations;

  useEffect(() => {
    if (currentAgent) {
      // 加载Agent配置
      // 加载历史聊天记录

      if (beLoadHistoryMessageConversationId) {
        loadAgentParameters(currentAgent).then((agentParam) => {
          const updateDefaultAgentWrapper = (message: any) => {
            updateDefaultAgentStateByMessage(message, currentAgent, defaultAgent, setDefaultAgentIsOpenThink, setLlmDefaultSelect);
          };
          loadHistoryMessage(beLoadHistoryMessageConversationId, currentAgent, setMessages, setMessageFeedBackDict, updateDefaultAgentWrapper).then(() => {
            setBeLoadHistoryMessageConversationId('')
          });
        })
      } else {
        Promise.all([loadAgentParameters(currentAgent), loadConversation()]).then(
            ([agentParam, conversations]) => {
              setActiveKey(`auto-${conversations.length}`);
              if (agentParam) {
                initializeMessage(agentParam, setMessages, currentAgent, defaultAgent);
              }
              if (content) {
                onSubmit(content);
              }
            }
        );
      }
    }
  }, [currentAgent]);

  // useEffect(() => {
  //   if (!activeKey.startsWith('auto') && currentAgent) {
  //     loadConversation(currentAgent);
  //   }
  // }, [activeKey]);

  // ========================== ServerFunction =========================



  const handleFileBeforeUpload = (file: RcFile) => {
    // console.log('fileType', file);

    if (!currentAgentParam) {
      return false;
    }

    const fileType = file.type.split('/')[0];
    // const fileExt = file.type.split('/')[1];

    const fileName = file.name.toLowerCase();
    const fileExt = fileName.split('.').pop() || '';

    // 检查是否已有上传文件，如果有，检查新上传文件的类型是否与已上传文件类型一致
    if (attachedFiles && attachedFiles.length > 0) {
      // 获取第一个已上传文件的类型
      const firstFile = attachedFiles[0];
      const firstFileType = firstFile.type.split('/')[0];

      // 如果第一个文件不是图片类型，检查是否为文档类型
      if (firstFileType !== 'image') {
        const firstFileName = firstFile.name.toLowerCase();
        const firstFileExt = firstFileName.split('.').pop() || '';
        const allowedExtensions = [
          'txt', 'md', 'mdx', 'markdown',
          'pdf', 'html', 'xlsx', 'xls',
          'docx', 'csv', 'eml', 'msg',
          'pptx', 'ppt', 'xml', 'epub'
        ];

        // 如果第一个文件是文档类型，新文件也应该是文档类型
        if (allowedExtensions.includes(firstFileExt)) {
          // 检查新文件是否也是文档类型
          if (fileType === 'image' || !allowedExtensions.includes(fileExt)) {
            antdMessage.error(`不能混合上传不同类型的文件，当前已上传文档类型文件`);
            return Upload.LIST_IGNORE;
          }
        }
      } else if (firstFileType === 'image') {
        // 如果第一个文件是图片类型，新文件也应该是图片类型
        if (fileType !== 'image') {
          antdMessage.error(`不能混合上传不同类型的文件，当前已上传图片类型文件`);
          return Upload.LIST_IGNORE;
        }
      }
    }

    if (
        currentAgentParam.file_upload.allowed_file_types &&
        currentAgentParam.file_upload.allowed_file_types.length > 0
    ) {
      // 判断image是否在包含在 fileType中
      if (currentAgentParam.file_upload.allowed_file_types.includes(fileType)) {
        return true;
      } else if (currentAgentParam.file_upload.allowed_file_types.includes('document')) {
        const allowedExtensions = [
          'txt', 'md', 'mdx', 'markdown',
          'pdf', 'html', 'xlsx', 'xls',
          'docx', 'csv', 'eml', 'msg',
          'pptx', 'ppt', 'xml', 'epub'
        ];
        if (allowedExtensions.includes(fileExt)) {
          return true;
        } else {
          antdMessage.error(`不支持的文件类型: ${fileExt.toUpperCase()}`);
          return Upload.LIST_IGNORE;
        }
      } else {
        antdMessage.error(`不支持的文件类型: ${fileType}`);
        return Upload.LIST_IGNORE;
      }
    }

    if (
        currentAgentParam.file_upload.allowed_file_extensions &&
        currentAgentParam.file_upload.allowed_file_extensions.length > 0
    ) {
      if (currentAgentParam.file_upload.allowed_file_types.includes(fileExt)) {
        return true;
      } else {
        antdMessage.error(`不支持的文件类型: ${fileExt}`);
        return Upload.LIST_IGNORE;
      }
    }
  };

  const handleFileUpload = async (file: any) => {
    if (!currentAgent) {
      return;
    }

    const encodedFileName = encodeURIComponent(file.file.name);
    const fileWithEncodedName = new File([file.file], encodedFileName, { type: file.file.type });

    const res = await upload(fileWithEncodedName, currentAgent.id);
    file.onSuccess(res);
  };

  const handleLogoutBtn = () => {
    logout();
  };

  const handleAgentManagerClick = () => {
    setContentShowState('agentManager');
    // 清除选中的 Agent ID
    setSelectedAgentId('');
  }

  const handleCreateAgentBtnClick = () => {
    // 确保在打开弹窗前先设置 agentFormData 为 null
    // 延迟一下再打开弹窗，确保状态更新完成
    setAgentFormData(null);
    setTimeout(() => {
      // 然后打开弹窗
      setIsOpenAgentFormModal(true);
    }, 10);
  }

  const handleEditAgentBtnClick = (agent: AgentDict) => {
    // 编辑时设置 agentFormData 并打开弹窗
    setAgentFormData(agent);
    // 延迟一下再打开弹窗，确保状态更新完成
    setTimeout(() => {
      setIsOpenAgentFormModal(true);
    }, 10);
  }

  const handleAddToList = (agent: AgentDict) => {
    const userAgent: UserAgentList = {
      agentDictId: agent.id,
    }
    return addUserAgent(userAgent).then(() => {
      antdMessage.success('添加成功');
      fetchAgentList();
    })
  }

  const handleFeedback = (message: string, rating: string | null) => {
    if (!currentAgent) {
      return;
    }
    feedback(currentAgent.id, message, rating).then(() => {
      antdMessage.success('反馈成功');
      setMessageFeedBackDict((prevState) => ({
        ...prevState,
        [message]: rating
      }))
    })
  }

  const handleRebuild = (content: string, messageId: string) => {
    if (!currentAgent || !messageId) {
      return;
    }

    onSubmit(content, currentAgent, messageId);
  }

  // ==================== Runtime ====================
  const updateConversionId = React.useRef((conversationId: string) => {
    if (activeKey !== conversationId) {
      setActiveKey(conversationId);
    }
  });

  const updateTaskId = React.useRef((taskId: string) => {
    if (currentTaskId !== taskId) {
      setCurrentTaskId(taskId);
    }
  });

  const cancelRef = React.useRef(() => {});



  /**
   * 页面初始化时执行的操作
   */
  useEffect(() => {

    // 音频播放器状态变更回调已迁移到 useAudioPlayer Hook

    // 添加音频播放器事件监听
    const handleAudioEvent: AudioPlayerEventCallback = (event, data) => {
      switch (event) {
        case 'start':
          console.log('开始播放音频:', data?.text);
          break;
        case 'play':
          console.log('正在播放音频:', data?.text);
          break;
        case 'buffer':
          console.log('音频缓冲中');
          break;
        case 'stop':
          console.log('停止播放音频');
          break;
        case 'end':
          console.log('音频播放结束');
          break;
        case 'error':
          console.error('音频播放错误:', data?.error);
          break;
      }
    };

    audioPlayer.addEventListener(handleAudioEvent);

    const fetchUserInfo = async () => {
      const userInfo = await getUserInfo();
      setUserItem(userInfo);
      if (userInfo && userInfo.workNo === '1044010100000218') {
        setIsCanCreateAgent(true);
      }
    };

    const fetchDefaultAgent = async () => {
      const defaultAgent = await getAgentDictByIsDefaultTrue();
      if (defaultAgent && defaultAgent.payload) {
        setDefaultAgent(defaultAgent.payload);
        setCurrentAgent(defaultAgent.payload);
        handleUpdateOpenThinkSwitch(defaultAgent, llmDefaultSelect);
      }
    };

    fetchDefaultAgent();
    fetchUserInfo();
    fetchAgentList();

    tryInitWWEnv().then(() => {
      setTimeout(() => {
        initMicrophoneService()
      }, 1000);
    });
    initTTSAndSTTWS();

    return () => {
      updateConversionId.current('');
      updateTaskId.current('');
      cancelRef.current();

      // 关闭所有活跃的 EventSource 连接
      closeAllEventSources();
      console.log('组件卸载，关闭所有活跃的 EventSource 连接');

      audioPlayer.removeEventListener(handleAudioEvent);
    };
  }, []);

  const renderMarkdown: BubbleProps['messageRender'] = (content) => {

    // content = content.replace(/<think>/g, '<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 12px;" open> <summary> 思考过程... </summary>').replace(/<\/think>/g, '</details>');

    return (
      <Typography>
        {/* biome-ignore lint/security/noDangerouslySetInnerHtml: used in demo */}
        <div className="ai-content-class" dangerouslySetInnerHTML={{ __html: md.render(content) }} />
      </Typography>
    )
  };

  const retrieverPopover = (groupedData: Record<string, RetrieverResource[]>) => {
    return Object.entries(groupedData).map(([documentId, items]) => {
      const documentName = items[0]?.document_name || '无标题';

      return (
        <ConfigProvider
          theme={buttonTheme}
        >
          <Popover
            key={documentId}
            title={documentName}
            trigger="click"
            content={retrieverPopoverContent(items)}
          >
            <Tooltip title={documentName}>
              <Button className="m-2 w-40 overflow-hidden">
                <span className="w-full text-xs truncate">{documentName}</span>
              </Button>
            </Tooltip>
          </Popover>
        </ConfigProvider>
      );
    });
  };

  const retrieverPopoverContent = (items: RetrieverResource[]) => {
    return (
      <div
        style={{
          width: '500px',
          height: '300px',
          overflow: 'auto',
          paddingRight: 10,
          scrollbarColor: '#888 transparent',
          scrollbarWidth: 'thin'
        }}
      >
        {items.map((item) => (
          <div
            style={{
              padding: '10px',
              border: '1px solid #e8e8e8',
              borderRadius: '4px',
              marginTop: '10px',
              marginBottom: '10px'
            }}
          >
            {/* 用户名 */}
            <div className="flex w-12 items-center px-1.5 h-5 border border-gray-200 rounded-md mb-1">
              <span className="text-[11px] font-medium text-gray-500">
                # {item.segment_position}
              </span>
            </div>

            {/* 内容区域 */}
            <Typography.Paragraph>{item.content}</Typography.Paragraph>
          </div>
        ))}
      </div>
    );
  };

  const [agent] = useXAgent<AgentMessage>({
    request: async ({ message }, { onSuccess, onUpdate, onError }) => {
      if (!message) {
        onError(new Error('No message'));
        return;
      }

      if (!message.content || !message.agentId) {
        onError(new Error('No message or agent'));
        return;
      }

      let isDoReplaceStartThink = true;
      let isDoReplaceEndThink = false;

      let content = '';
      const conversationId = message.conversationId?.startsWith('auto')
        ? undefined
        : message.conversationId;

      onUpdate({
        type: 'ai',
        content: '',
        suggested: message.suggested
      });

      // 当使用 Chat SDK 时，不需要流式 API
      const readableStream = streamApi(message.content, message.agentId, conversationId, message.fileList, message.inputs, message.parentMessageId);

      const stream = XStream({
        readableStream: readableStream
      });

      const reader = stream.getReader();
      cancelRef.current = () => {
        reader?.cancel();
        if (!content) {
          onError(new Error('No message or agent'));
        }
      };

      while (reader) {
        const { value, done } = await reader.read();
        if (!value && done) {
          onSuccess({
            id: "",
            type: 'ai',
            content: content,
            suggested: message.suggested
          });
          break;
        }

        if (!value) continue;

        const response: StreamResponse = JSON.parse(value.data);

        if (done) {
          onSuccess({
            id: response.message_id,
            type: 'ai',
            content: content,
            suggested: message.suggested
          });
          break;
        }
        switch (value.event) {
          case 'workflow_started':
            updateConversionId.current(response.conversation_id);
            updateTaskId.current(response.task_id);
            break;
          case 'workflow_finished':
            break;
          case 'node_started':
            break;
          case 'node_finished':
            break;
          case 'message':
            content += response.answer;

            if (content.length > 10 && isDoReplaceStartThink) {
              if (content.startsWith("<think>")) {
                content = content.replace(/<think>/g, '<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 12px;" open> <summary> 思考过程... </summary>');
                isDoReplaceStartThink = false;
                isDoReplaceEndThink = true;
              }
            } else if (isDoReplaceEndThink && content.indexOf("</think>") > 5) {
              content = content.replace(/<\/think>/g, '</details>');
              isDoReplaceEndThink = false;
            }

            // 如果启用了实时播放，则调用audioPlayer.playRealtimeText
            if (message.isAudioPlayer) {
              console.log( '实时播放:', response.answer);
              audioPlayer.playRealtimeText(response.answer);
            }

            onUpdate({
              id: response.message_id,
              type: 'ai',
              content: content,
              suggested: message.suggested
            });
            break;
          case 'message_end':
            {
              // 如果启用了实时播放，发送流式文本结束信号
              if (message.isAudioPlayer) {
                audioPlayer.endRealtimePlay();
              }

              let retrieverResources = [];
              // 获取 metadata 里面的内容，看看有没有引用
              if (
                response.metadata &&
                response.metadata.retriever_resources &&
                response.metadata.retriever_resources.length > 0
              ) {
                retrieverResources = response.metadata.retriever_resources;
              }

              let suggestion = [];
              // 判断agent参数
              if (message.suggested) {
                onUpdate({
                  id: response.message_id,
                  type: 'ai',
                  content: content,
                  suggested: message.suggested
                });
                // 获取下一步建议
                const suggests = await suggested(response.message_id, message.agentId);
                suggestion = suggests.data;
              }

              onSuccess({
                id: response.message_id,
                type: 'ai',
                content: content,
                suggested: message.suggested,
                list: suggestion,
                retrieverResources: retrieverResources
              });
            }
            break;
          case 'error':
            break;
        }
      }
    }
  });

  const { onRequest, messages, setMessages, parsedMessages } = useXChat({
    agent,
    parser: (msg) => {
      if (msg.list && msg.list.length > 0) {
        const array = [
          {
            id: msg.id,
            type: msg.type,
            content: msg.content,
            retrieverResources: msg.retrieverResources
          },
          {
            id: msg.id,
            type: 'suggestion',
            content: msg.list
          }
        ];
        return array;
      }

      return msg;
    }
  });

  const items: GetProp<typeof Bubble.List, 'items'> = parsedMessages.map(
    ({ id, message, status }) => {
      let content;
      let loading = false; // 默认 loading 为 false
      let isShowFooter = true; // 控制页脚可见性的标志
      let lId = id; // 如果消息有 id，使用消息 id 作为 key

      if (message.id) {
        lId = message.id;
      }

      // 找到 parsedMessages 数组中最后一个 AI 消息的索引
      // const lastAIMessageIndex = parsedMessages.slice().reverse().findIndex(msg => msg.message.type === 'ai');
      // 判断当前消息是否是最后一个 AI 消息
      // const isLastAIMessage = lastAIMessageIndex !== -1 && index === parsedMessages.length - 1 - lastAIMessageIndex;

      // --- 根据消息类型和状态确定内容和 loading 状态 ---
      if (message.type === 'ai') {
        // 对于 AI 消息，如果它是最后一个 AI 消息且 agent 正在请求中，则设置 loading 为 true
        // 这会向 Bubble.List 发出信号，表明此消息正在被“打字”输出
        // console.log("agent.isRequesting", agent.isRequesting());
        // if (isLastAIMessage && agent.isRequesting()) {
        //   loading = true;
        // } else {
        //   // 确保已完成的 AI 消息 loading 为 false
        //   loading = false;
        // }

        // 如果内容可用，渲染 markdown 内容
        if (message.content && typeof message.content === 'string' && message.content.length > 0) {
          content = renderMarkdown(message.content);
        } else if (status === 'success') {
          content = ''; // 让 Bubble 处理打字动画显示
          isShowFooter = false; // 打字时隐藏页脚
        } else {
          loading = true;
        }

        // 处理成功 AI 消息的检索资源
        if (
          status === 'success' && // 只为成功消息显示资源
          currentAgentParam?.retriever_resource?.enabled &&
          message.retrieverResources &&
          message.retrieverResources.length > 0
        ) {
          loading = false; // 显示资源时确保 loading 为 false
          // 按文档 ID 分组检索资源
          const grouped: Record<string, RetrieverResource[]> = {};
          message.retrieverResources.forEach((reteriever) => {
            const key = reteriever.document_id;
            if (!grouped[key]) {
              grouped[key] = [];
            }
            grouped[key].push(reteriever);
          });

          // 结合 markdown 内容和检索链接
          content = (
            <>
              {renderMarkdown(message.content)}
              <div>
                <Divider
                  style={{ margin: '5px 0', borderColor: 'rgba(0,0,0,.25)' }}
                  orientation="left"
                >
                  引用
                </Divider>
                <div>{retrieverPopover(grouped)}</div>
              </div>
            </>
          );
        }

      } else if (message.type === 'local') {

        // 渲染用户消息内容和附件
        if (message.content && typeof message.content === 'string' && message.content.length > 0) {
          if (message.fileList && message.fileList.length > 0) {
            content = (
              <>
                {/* 映射并渲染附件 (图片或文档) */}
                {message.fileList.map((file, fileIndex) => {
                  let dom;
                  if (file.type === 'document') {
                    dom = (<Attachments.FileCard key={file.file.uid || fileIndex} item={file.file} />);
                  } else {
                    dom = (
                      <Image.PreviewGroup key={fileIndex}>
                        <Image
                          width={100}
                          src={file.url ? file.url : (file.file.originFileObj ? URL.createObjectURL(file.file.originFileObj) : '')} // 使用 URL 或 createObjectURL
                          style={{ marginRight: '10px' }}
                        />
                      </Image.PreviewGroup>
                    );
                  }
                  return dom;
                })}
                {/* 渲染用户的文本内容 */}
                {renderMarkdown(message.content)}
              </>
            );
          } else {
            // 只渲染用户的文本内容
            content = renderMarkdown(message.content);
          }
        } else if (loading) {
          // 本地消息正在加载但没有内容 (例如，发送没有文本的文件)
          content = '发送中...';
          isShowFooter = false; // 发送时隐藏页脚
        } else {
          // 如果不是加载中 (发送成功)，应该有内容
          content = ''; // 或者处理空消息的情况
        }

      } else if (message.type === 'suggestion') {
        // 处理建议消息
        content = message.content; // 内容是建议数组
        loading = false; // 建议不加载
        isShowFooter = false; // 建议没有页脚
      } else {
        // 处理其他潜在的消息类型或初始状态
        content = message.content;
        loading = false;
        isShowFooter = false;
      }

      // --- 确定页脚可见性和内容 ---
      let footer = null;
      // 仅当不加载且 isShowFooter 为 true (且状态为 success 用于最终内容，如反馈) 时显示页脚
      // 根据您希望页脚何时出现来调整此条件 (例如，仅用于已完成的 AI 回复)
      if (!loading && isShowFooter && status === 'success' && message.type === 'ai') { // 仅为已完成的 AI 消息显示页脚
        footer = (
          <div
            style={{ display: 'flex', justifyContent: 'space-between', width: '96%', marginLeft: 10 }}
          >
            <div style={{ display: 'flex' }}>
              {/* 复制按钮 */}
              <Tooltip title="复制">
                <CopyToClipboard text={typeof message.content === 'string' ? message.content : ''} onCopy={() => {
                  antdMessage.success('内容已复制到剪贴板');
                }}>
                  <Button
                    color="default"
                    variant="text"
                    size="small"
                    icon={<CopyOutlined />}
                  />
                </CopyToClipboard>
              </Tooltip>
              {/* 播放按钮 - 仅在未开启实时播放时显示 */}
              {!audioPlayerHook.realtimePlayEnabled && (
                <PlayButton
                  message={typeof message.content === 'string' ? message.content : ''}
                  disabled={!message.content}
                />
              )}
            </div>
            {/* 反馈按钮 (点赞/点踩) */}
            <div className="flex items-center">
              <Tooltip title={messageFeedBackDict[lId] === 'like' ? '取消点赞' : '点赞'}>
                <Button
                  color="default"
                  variant="text"
                  size="small"
                  icon={messageFeedBackDict[lId] === 'like' ? <LikeFilled style={{color: '#1890ff'}} /> : <LikeOutlined />}
                  onClick={() => handleFeedback(lId, messageFeedBackDict[lId] === 'like' ? null : 'like')}
                  style={{ marginRight: 10}}
                />
              </Tooltip>
              <Tooltip title={messageFeedBackDict[lId] === 'dislike' ? '取消点踩' : '点踩'}>
                <Button
                  color="default"
                  variant="text"
                  size="small"
                  icon={messageFeedBackDict[lId] === 'dislike' ? <DislikeFilled style={{color: '#dc2626'}} /> : <DislikeOutlined />}
                  onClick={() => handleFeedback(lId, messageFeedBackDict[lId] === 'dislike' ? null : 'dislike')}
                />
              </Tooltip>
            </div>
          </div>
        );
      }

      // 在 AI 消息成功后延迟加载对话 (原逻辑)
      // 这可能需要根据您期望的行为进行调整
      if (status === 'success' && message.type === 'ai' && id !== 'welcome' && currentAgent) {
        setTimeout(() => {
          loadConversation(activeKey, currentAgent.id).then()
        }, 1000)
      }

      // 返回用于 Bubble.List 的消息项结构
      return {
        key: lId, // 消息项的唯一 key
        loading: loading, // 传递确定的 loading 状态
        role: message.type, // 使用 message.type 作为角色 ('ai', 'local', 'suggestion')
        content: content, // 要显示的消息内容
        footer: footer, // 消息页脚 (复制、反馈等)
      };
    }
  );

  const roles: GetProp<typeof Bubble.List, 'roles'> = {
    ai: {
      placement: 'start',
      typing: {step: 1, interval: 500},
      styles: {
        content: {
          backgroundColor: 'rgba(255,255,255)',
          marginLeft: 10,
          marginRight: 10,
          paddingBottom: 0
        },
        footer: {
          width: '100%'
        }
      }
    },
    local: {
      placement: 'end',
      styles: {
        content: {
          backgroundColor: 'rgba(0,0,0,0.04)',
          color: 'rgba(0,0,0,0.85)',
          borderRadius: 12,
          fontSize: 15,
          marginLeft: 10,
          marginRight: 10
        },
        footer: {
          width: '100%'
        }
      },
      shape: 'round',
    },
    suggestion: {
      placement: 'start',
      variant: 'borderless',
      messageRender: (content) => (
        <Prompts
          vertical={true}
          items={(content as any as string[]).map((text) => ({
            key: text,
            description: (
              <div>
                <span>{text}</span>
                <span style={{ marginLeft: 10 }}>
                  <ArrowRightOutlined />
                </span>
              </div>
            )
          }))}
          onItemClick={(key) => {
            handleSuggestionPromptsClick(key.data);
          }}
          style={{
            marginLeft: 20
          }}
          classNames={{
            item: styles.suggestionPrompts
          }}
        />
      )
    }
  };

  const conversationsMenu: ConversationsProps['menu'] = (conversation) => ({
    items: [
      {
        label: conversation['isTop'] ? '取消固定' : '固定',
        key: 'top',
        icon: <PushpinOutlined />
      },
      {
        label: '重命名',
        key: 'rename',
        icon: <EditOutlined />
      },
      {
        label: '删除',
        key: 'delete',
        icon: <DeleteOutlined />,
        danger: true
      }
    ],
    onClick: (menuInfo) => {
      if (!currentAgent) {
        return;
      }
      const isTop: boolean = conversation['isTop'];

      // 根据menuInfo.key分别调用不同的方法
      switch (menuInfo.key) {
        case 'top':
          if (!isTop) {
            setRenameModalType('topChat');
            setBeRenameConversionId(conversation.key);
            chatNameForm.setFieldValue('name', conversation.label);
            // setNewName(conversation.label);
            setIsShowRenameModal(true);
          } else {
            topChatHistory(conversation.key, false).then((res) => {
              if (res) {
                loadConversation();
                antdMessage.success('操作成功');
              }
            })
          }
          setRenameModalType('topChat');

          break;
        case 'rename':
          setRenameModalType('rename');
          setBeRenameConversionId(conversation.key);
          chatNameForm.setFieldValue('name', conversation.label);
          // setNewName(conversation.label);
          setIsShowRenameModal(true);
          break;
        case 'delete':
          setIsShowSpin(true);
          deleteChatHistory(conversation.key).then((res) => {
          // deleteConversation(currentAgent.id, conversation.key).then((res) => {
            if (res) {
              setIsShowSpin(false);
              loadConversation();
              antdMessage.success('删除成功');
            }
          });
          break;
      }
    },
    trigger: (menuInfo) => {
      if (collapsed) {
        return (<></>);
      }
      return menuInfo['isTop'] ?
          (<PushpinOutlined style={{color: 'rgba(0, 0, 0, 0.88)'}}
            onClick={(event) => {
              event.stopPropagation();
            }}
          />) :
          (<MoreOutlined style={{color: 'rgba(0, 0, 0, 0.88)'}}
              onClick={(event) => {
                event.stopPropagation();
              }}
          />)
    },
  });



  // ==================== Event ====================
  const handleConfirmRename = () => {
    if (!currentAgent || !beRenameConversionId) {
      return;
    }

    if (!chatNameForm.getFieldValue('name')) {
      antdMessage.error('请输入新的名字');
      return;
    }

    setIsShowSpin(true);

    if (renameModalType === 'topChat') {
      topChatHistory(beRenameConversionId, true, chatNameForm.getFieldValue('name')).then((res) => {
        if (res) {
          setIsShowSpin(false);
          chatNameForm.setFieldValue('name', '');
          setBeRenameConversionId('');
          setIsShowRenameModal(false);
          loadConversation();
          antdMessage.success('操作成功');
        }
      })
    } else {
      renameChatHistory(beRenameConversionId, chatNameForm.getFieldValue('name')).then((res) => {
        // renameConversation(currentAgent.id, beRenameConversionId, newName).then((res) => {
        if (res) {
          setIsShowSpin(false);
          chatNameForm.setFieldValue('name', '');
          // setNewName('');
          setBeRenameConversionId('');
          setIsShowRenameModal(false);
          loadConversation();
        }
      });
    }
  };

  const handleCancelRename = () => {
    setIsShowSpin(false);
    chatNameForm.setFieldValue('name', '');
    // setNewName('');
    setIsShowRenameModal(false);
  };

  const onSubmit = (nextContent: string, agent?: AgentDict, parentMessageId?: string) => {
    if (!nextContent || !currentAgent) return;

    let defAgent = currentAgent;

    if (agent) {
      defAgent = agent;
    }

    const fileList: DifyFile[] = [];
    if (attachedFiles && attachedFiles.length > 0) {
      attachedFiles.forEach((file) => {
        let type = file.type.split('/')[0];
        if (type !== 'image') {
          const fileName = file.name.toLowerCase();
          const fileExt = fileName.split('.').pop() || '';
          const allowedExtensions = [
            'txt', 'md', 'mdx', 'markdown',
            'pdf', 'html', 'xlsx', 'xls',
            'docx', 'csv', 'eml', 'msg',
            'pptx', 'ppt', 'xml', 'epub'
          ];
          if (allowedExtensions.includes(fileExt)) {
            type = "document";
          }
        }

        fileList.push({
          type: type,
          transfer_method: 'local_file',
          upload_file_id: file.response.id,
          file: file
        });
      });
    }

    // 判断是否是默认agent，如果是就添加 LLM 参数
    let inputs = {};
    if (currentAgent.isDefault) {
      let defaultSelect = llmDefaultSelect;
      if (llmDefaultSelect === 'Qwen2.5:32B-instruct' && defaultAgentIsOpenThink) {
        defaultSelect = 'QwQ:32B';
      }

      inputs = {
        'llm': defaultSelect
      }
    }

    // 生成请求的 json
    const agentMessage: AgentMessage = {
      type: 'local',
      agentId: defAgent.id,
      conversationId: activeKey,
      suggested: currentAgentParam && currentAgentParam.suggested_questions_after_answer
        ? currentAgentParam.suggested_questions_after_answer.enabled
        : false,
      content: nextContent,
      fileList: fileList,
      inputs: inputs,
      isAudioPlayer: audioPlayerHook.realtimePlayEnabled,
      parentMessageId: parentMessageId
    };

    setIsBeRefreshConversations(true);
    onRequest(agentMessage);
    setContent('');
    setAttachedFiles([]);
    setHeaderOpen(false);
  };

  const onCancel = () => {
    if (currentAgent && currentTaskId) {
      stop(currentAgent.id, currentTaskId).then(() => {
        loadConversation(activeKey, currentAgent.id).then()
      });
      cancelRef.current();
    }
  };

  const onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'> = (info) => {
    // onRequest(info.data.description as string);

    if (info.data.key.startsWith('1')) {
      // const apiKey = 'app-VKAGFGvqBqE6P0zT77fkTSEP'
      const apiKey = '0wcJk1';
      // 在 agentlist 中找对应 apikey 的对象
      if (agentList.some((agent) => agent.apiKey.includes(apiKey))) {
        // 找到对应的 agent
        const selectedAgent = agentList.find((agent) => agent.apiKey.includes(apiKey));
        if (selectedAgent) {
          // 设置当前 agent 为选中的 agent
          setContent(info.data.description as string);
          handleAgentClick(selectedAgent);
        }
      }
    } else if (info.data.key.startsWith('2')) {
      const apiKey = 'BsS44M';
      if (agentList.some((agent) => agent.apiKey.includes(apiKey))) {
        // 找到对应的 agent
        const selectedAgent = agentList.find((agent) => agent.apiKey.includes(apiKey));
        if (selectedAgent) {
          // 设置当前 agent 为选中的 agent
          setContent(info.data.description as string);
          handleAgentClick(selectedAgent);
        }
      }
    }
  };

  const onAddConversation = (llmSelect: string) => {
    // console.log('onAddConversation', activeKey);

    // 关闭所有活跃的 EventSource 连接
    closeAllEventSources();
    // 取消当前请求
    cancelRef.current();

    setContentShowState('defaultChat')
    setCurrentAgent(defaultAgent);
    handleUpdateOpenThinkSwitch(defaultAgent, llmSelect);
    // if (!activeKey.startsWith('auto') && defaultAgent) {
    //   loadConversation(defaultAgent);
    // }
    setActiveKey(`auto-${conversationsItems.length + 1}`);
    setMessages([]);
    setNodeMenuSelectedKey('');
    // 清除选中的 Agent ID
    setSelectedAgentId('');

    if (isMobile) {
      setDrawerOpen(false);
    }
  };

  const onConversationClick: GetProp<typeof Conversations, 'onActiveChange'> = (key) => {
    // console.log('onConversationClick', key);
    onCancel();

    // 关闭所有活跃的 EventSource 连接
    closeAllEventSources();
    // 取消当前请求
    cancelRef.current();

    // 根据 key 在conversationsItems 中查找对应的 对象
    const selectedConversation = conversationsItems.find((item) => item.key === key);
    if (selectedConversation) {
      setActiveKey(selectedConversation.conversationId);
      setBeLoadHistoryMessageConversationId(selectedConversation.conversationId);

      // 判断AgentId和当前的currentAgentId 是不是同一个 ID
      if (selectedConversation.agentDictId !== currentAgent?.id) {
        // 如果不是同一个 ID，则设置当前 agent 为选中的 agent
        handleAgentClick(selectedConversation.agentDict);
        // 注意：handleAgentClick 函数已经会根据 agent 类型设置正确的 contentShowState
      } else {
        // 如果是同一个 ID，则直接加载历史消息

        // 无论是什么类型，都设置为问答页面
        setContentShowState('defaultChat');

        const updateDefaultAgentWrapper = (message: any) => {
          updateDefaultAgentStateByMessage(message, currentAgent, defaultAgent, setDefaultAgentIsOpenThink, setLlmDefaultSelect);
        };
        loadHistoryMessage(selectedConversation.conversationId, currentAgent, setMessages, setMessageFeedBackDict, updateDefaultAgentWrapper).then(() => {
          setBeLoadHistoryMessageConversationId('');
        });
      }
    } else {
      setContentShowState('defaultChat');
      setActiveKey(key);
    }

    if (isMobile) {
      setDrawerOpen(false);
    }
  };

  const handleFileChange: GetProp<typeof Attachments, 'onChange'> = (info) => {
    setAttachedFiles(info.fileList);
    // console.log('uploadOnchange', info);
  };

  const handleLoginBtn = () => {
    // console.log('login', localStorage.getItem('userItem'));
  };

  const handleAgentClick = (agent: AgentDict) => {
    // console.log('Selected Agent:', agent);
    onCancel();

    // 关闭所有活跃的 EventSource 连接
    closeAllEventSources();
    // 取消当前请求
    cancelRef.current();

    // 如果是SuperSonic类型，在当前页面显示 iframe
    if (agent.type === 'supersonic') {
      antdMessage.info('正在加载 SuperSonic 问答页面...', 2);

      // 设置当前 SuperSonic agent ID
      setCurrentSuperSonicAgentId(agent.id);

      // 如果有自定义的 SuperSonic 服务器地址和 Agent ID，保存到当前参数中
      if (agent.superSonicServerHost) {
        console.log(`使用自定义 SuperSonic 服务器地址: ${agent.superSonicServerHost}`);
      }

      if (agent.superSonicAgentId) {
        console.log(`使用自定义 SuperSonic Agent ID: ${agent.superSonicAgentId}`);
      }

      // 切换到 SuperSonic 内容状态
      setContentShowState('supersonic');
    } else {
      // 其他类型的agent保持原有行为
      setContentShowState('agent');
      // 如果不是 SuperSonic 类型，从服务器加载参数
      loadAgentParameters(agent);
    }

    handleUpdateOpenThinkSwitch(agent, llmDefaultSelect);
    setCurrentAgent(agent);
    setNodeMenuSelectedKey(agent.id);
    // 设置选中的 Agent ID
    setSelectedAgentId(agent.id || '');

    if (isMobile) {
      setDrawerOpen(false);
    }
  };

  const handleSuggestionPromptsClick = (item: PromptProps) => {
    // console.log(item);

    // 如果是 SuperSonic 的建议问题，直接提交问题内容
    if (item.key.toString().startsWith('supersonic-')) {
      // 使用 description 而不是 title，因为 PromptProps 没有 title 属性
      // 将 description 转换为字符串
      onSubmit(String(item.description || ''));
    } else {
      // 其他类型的建议问题，按原来的方式处理
      onSubmit(item.key);
    }
  };

  const handleLLMDropDownClick = (info: any) => {
    // console.log('handleLLMDropDownClick', info);
    setLlmDefaultSelect(info.key);
    localStorage.setItem('llmDefaultSelect', info.key)
    onAddConversation(info.key);
  }

  const handleUpdateOpenThinkSwitch = (agent:AgentDict | null, llmName: string) => {
    console.log('handleUpdateOpenThinkSwitch', agent, llmName);
    // 判断是否为默认Agent
    if (agent && agent.id !== defaultAgent?.id) {
      setDefaultAgentIsOpenThink(false);
      setIsDisableSwitchOpenThink(true);
      return;
    }

    if (llmName === 'DeepSeek-R1:671B') {
      setDefaultAgentIsOpenThink(true);
      setIsDisableSwitchOpenThink(true);
    } else {
      setDefaultAgentIsOpenThink(false);
      setIsDisableSwitchOpenThink(false);
    }
  }

  const handleAgentCardMoreDropDownClick = (info: any, item: AgentDict) => {
    // console.log('handleAgentCardMoreDropDownClick', info);
    const event = info.domEvent; // 获取事件对象
    event.stopPropagation(); // 阻止事件冒泡
    switch (info.key) {
      case 'addToList': {
        if (item.userAgentId) {
          antdMessage.error('该智能体已添加到列表中，请勿重复添加');
          return;
        }
        handleAddToList(item);
      }
      break;
      case 'visibility': {
        setVisibleSelectAgentDictId(item.id);
        setIsShowVisibleSelectorDialog(true);
      }
      break;
      case 'remove': {
        // 删除当前 agent
        deleteAgentDict(item.id).then(() => {
          // console.log('deleteAgentDict');
          antdMessage.success('删除成功');
          fetchAgentList();
          loadConversation();
        })
      }
        break;
      case 'edit': {
        // 编辑当前 agent
        handleEditAgentBtnClick(item);
      }
        break;
    }
  }

  const handleAgentListMoreDropDownClick = (menuInfo: any, userAgent: UserAgentList) => {
    // console.log('handleAgentListMoreDropDownClick', menuInfo);
    if (!userAgent) {
      return;
    }

    switch (menuInfo.key) {
      case 'top':
        setTopUserAgent(userAgent.id, !userAgent.isTop).then(() => {
          fetchAgentList();
          antdMessage.success('操作成功');
        })
        break;
      case 'remove':
        setIsShowSpin(true);
        deleteUserAgent(userAgent.id).then(() => {
          fetchAgentList();
          antdMessage.success('删除成功');
        }).finally(() => {
          setIsShowSpin(false);
        })
    }

  }

  const handleAgentManagerAgentClick = (agent: AgentDict) => {
    // console.log('handleAgentManagerAgentClick', agent);
    if (isCanCreateAgent) {
      return;
    }

    if (agent.userAgentId) {
      antdMessage.error('该智能体已添加到列表中，请勿重复添加');
      return; // 如果已添加，直接返回，不执行后续操作
    }

    // 添加到列表
    handleAddToList(agent).then((addedAgent) => {
      // 如果是 SuperSonic 类型，添加后自动在当前页面显示
      if (addedAgent && addedAgent.type === 'supersonic') {
        // 显示提示消息
        antdMessage.success('已添加 SuperSonic 智能体，正在加载问答页面...');

        // 设置当前 SuperSonic agent ID
        setCurrentSuperSonicAgentId(addedAgent.id);

        // 切换到 SuperSonic 内容状态
        setContentShowState('supersonic');

        // 设置当前 Agent 和菜单选中状态
        setCurrentAgent(addedAgent);
        setNodeMenuSelectedKey(addedAgent.id);
      }
    });
  }

  const handleSpeechRecordingChange = (recording: boolean) => {
    console.log('handleSpeechRecordingChange', recording);
    if (recording) {
      startRecording(() => {
        setIsSpeechRecording(recording)
      }, (error) => {
        antdMessage.error(error);
        setIsSpeechRecording(false);
      })
    } else {
      stopRecording((text: string) => {
        setIsSpeechRecording(recording)
        if (text) {
          setContent(text);
        }
      }, (error) => {
        antdMessage.error(error);
        setIsSpeechRecording(true);
      })
    }
  }

  /**
   * 尝试初始化企业微信环境
   */
  const tryInitWWEnv = async () => {
    try {
      await tryRegisterWW();
    } catch (e) {
      console.error('Error registering WeWork:', e);
    }
  }

  const initTTSAndSTTWS = () => {
    try {
      initTTSWebSocket();
      initSTTWebSocket();
    } catch (e) {
      console.error('Error initializing TTS and STT:', e);
    }
  }

  /**
   * 切换实时播放状态
   */
  const toggleRealtimePlay = () => {
    audioPlayerHook.toggleRealtimePlay(!audioPlayerHook.realtimePlayEnabled);
  }

  // ==================== Nodes ====================
  const placeholderNode = (
    <WelcomePlaceholder
      currentAgent={currentAgent}
      className={styles.placeholder}
    />
  );

  const attachmentsNode = (
      <ConfigProvider
        theme={{
          components: {
            Button: {
              defaultColor: 'rgba(0, 0, 0, 0.85)',
              defaultHoverBg: 'rgba(0, 0, 0, 0.1)',
              defaultHoverColor: 'rgba(0, 0, 0, 0.85)',
              defaultHoverBorderColor: 'rgb(217, 217, 217)',
            }
          }
        }}
      >
        <Button icon={<FileAddOutlined />}
                onClick={() => setHeaderOpen(!headerOpen)}
                style={{
                  marginRight: '10px',
                }}
        >
          <Badge dot={attachedFiles.length > 0 && !headerOpen}>
            上传参考文件
          </Badge>
        </Button>
      </ConfigProvider>
  );

  const senderHeader = (
    <Sender.Header
      title="附件上传"
      open={headerOpen}
      onOpenChange={setHeaderOpen}
      styles={{
        content: {
          padding: 0
        }
      }}
    >
      <Attachments
        beforeUpload={handleFileBeforeUpload}
        items={attachedFiles}
        onChange={handleFileChange}
        customRequest={handleFileUpload}
        maxCount={8}
        placeholder={(type) =>
          type === 'drop'
            ? { title: '拖动文件到此处' }
            : {
                icon: <CloudUploadOutlined />,
                title: '附件上传',
                description: '单击或拖动文件到此区域上传附件'
              }
        }
        disabled={attachedFiles.length >= 8}
      />
    </Sender.Header>
  );

  const logoNode = (
    <LogoSection isLogoSpanVisible={isLogoSpanVisible} />
  );

  const newConversitionNode = (
    <NewConversationButton
      onAddConversation={onAddConversation}
      llmDefaultSelect={llmDefaultSelect}
      currentAgent={currentAgent}
      isLogoSpanVisible={isLogoSpanVisible}
    />
  )

  const historicalDialoguesNode = (
    <HistoricalDialogues
      conversationsItems={conversationsItems}
      activeKey={activeKey}
      onConversationClick={onConversationClick}
      conversationsMenu={conversationsMenu}
    />
  )

  const userSectionNode = (
    <UserSection
      userItem={userItem}
      onLogin={handleLoginBtn}
      onLogout={handleLogoutBtn}
    />
  );

  const agentListNode = (
    <AgentList
      currentUserAgentList={currentUserAgentList}
      selectedAgentId={selectedAgentId}
      collapsed={collapsed}
      contentShowState={contentShowState}
      nodeMenuSelectedKey={nodeMenuSelectedKey}
      onAgentClick={handleAgentClick}
      onAgentManagerClick={handleAgentManagerClick}
      onAgentListMoreDropDownClick={handleAgentListMoreDropDownClick}
    />
  );

  const pcLeftSider = (
      <>
        <Sider
            trigger={null}
            collapsible
            collapsed={collapsed}
            theme={'light'}
            width={270}
            style={{ backgroundColor: 'rgb(243, 244, 246)', border: '1px solid rgba(0, 0, 0, 0.08)' }}
        >
          <div className="flex flex-col h-full">
            {logoNode}
            {newConversitionNode}
            {historicalDialoguesNode}
            <Divider
                style={{
                  margin: '5px 12px',
                  minWidth: '0',
                  width: 'auto',
                  borderColor: 'rgba(0,0,0,.25)'
                }}
            />
            {agentList && agentListNode}
          </div>
        </Sider>
      </>
  )

  const mobileLeftSider = (
      <>
        <ConfigProvider
            theme={{
              components: {
                Drawer: {
                  paddingLG: 0
                }
              }
            }}
        >
          <Drawer
              placement={"left"}
              closable={false}
              open={drawerOpen}
              onClose={() => setDrawerOpen(false)}
              width={270}
              style={{ backgroundColor: 'rgb(243, 244, 246)', border: '1px solid rgba(0, 0, 0, 0.08)' }}
          >
            <div className="flex flex-col h-full">
              {logoNode}
              {newConversitionNode}
              {historicalDialoguesNode}
              <Divider
                  style={{
                    margin: '5px 12px',
                    minWidth: '0',
                    width: 'auto',
                    borderColor: 'rgba(0,0,0,.25)'
                  }}
              />
              {agentList && agentListNode}
            </div>
          </Drawer>
        </ConfigProvider>
      </>
  )

  const senderFooter = (
    <SenderFooter
      currentAgentParam={currentAgentParam}
      headerOpen={headerOpen}
      attachedFilesCount={attachedFiles.length}
      defaultAgentIsOpenThink={defaultAgentIsOpenThink}
      isDisableSwitchOpenThink={isDisableSwitchOpenThink}
      hasMessages={items.length > 0}
      realtimePlayEnabled={audioPlayerHook.realtimePlayEnabled}
      onFileUploadToggle={() => setHeaderOpen(!headerOpen)}
      onDeepThinkToggle={() => setDefaultAgentIsOpenThink(!defaultAgentIsOpenThink)}
      onRealtimePlayToggle={toggleRealtimePlay}
    />
  )

  // ==================== Render =================
  return (
    <>
      <Layout style={{ height: '100vh', overflow: 'hidden' }}>
        {isMobile ? mobileLeftSider : pcLeftSider}
        <Layout className="bg-white" style={{
          width: '100%',
          height: '100%',
          paddingRight: contentShowState === 'supersonic' ? 0 : 20,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          <Header className="bg-white p-0 flex justify-between items-center w-full" style={{
            paddingRight: contentShowState === 'supersonic' ? 20 : 0,
        }}>
            <div className="flex items-center">
              <MenuToggleButton
                collapsed={collapsed}
                isMobile={isMobile}
                onToggle={() => {
                  if (isMobile) setDrawerOpen(true)
                  else setCollapsed(!collapsed);
                }}
              />
              <LLMSelectorButton
                currentAgent={currentAgent}
                llmDefaultSelect={llmDefaultSelect}
                llmItems={llmItems}
                onLLMSelect={handleLLMDropDownClick}
              />
            </div>
            <div className="h-9 cursor-pointer">
              {/* 用户区域 */}
              {userSectionNode}
            </div>
          </Header>
          <Content className="w-full" style={{ height: 'calc(100vh - 64px)', overflow: 'hidden', padding: contentShowState === 'supersonic' ? 0 : '8px' }}>
            <div className={contentShowState === 'supersonic' ? '' : styles.chat} style={{
              height: '100%',
              width: contentShowState === 'supersonic' ? '100%' : undefined,
              maxWidth: contentShowState === 'supersonic' ? 'none' : undefined,
              padding: contentShowState === 'supersonic' ? 0 : undefined
            }}>
              {!isMobile && (<FloatingMenu />)}
              {(contentShowState === 'defaultChat' || contentShowState === 'agent') && (
                  <>
                    {/* 🌟 消息列表 */}
                    <Bubble.List
                        items={
                          items.length > 0 ? items : [{ content: placeholderNode, variant: 'borderless', style: { justifyContent: 'center' } }]
                        }
                        roles={roles}
                        className={`${styles.messages} ${items.length > 0 ? styles.messagesExpanded : styles.messagesCollapsed}`}
                    />
                    {/* 🌟 提示词 */}
                    {/*<Prompts items={senderPromptsItems} onItemClick={onPromptsItemClick} />*/}
                    {/* 🌟 输入框 */}
                    <Sender
                        value={content}
                        placeholder="有什么我能帮您的吗？"
                        header={senderHeader}
                        onSubmit={onSubmit}
                        onCancel={onCancel}
                        onChange={setContent}
                        loading={agent.isRequesting()}
                        className={`${styles.sender} ${items.length > 0 ? 'sender-bottom' : 'sender-center'}`}
                        disabled={!currentAgent}
                        classNames={{
                          input: "sender-input"
                        }}
                        footer={senderFooter}
                        allowSpeech={(() => {
                          return {
                            recording: isSpeechRecording,
                            onRecordingChange: handleSpeechRecordingChange
                          }
                        })()}
                    />
                  </>
              )}

              {contentShowState === 'supersonic' && (
                <SuperSonicChatWrapper
                  agent={currentAgent}
                />
              )}

              {contentShowState === 'agentManager' && (
                  <div className="w-full h-full flex flex-col">
                    {/* 标题加按钮，按钮有权限控制 */}
                    <div className="w-full h-[100px] flex flex-row items-center justify-between mb-4">
                      <div className="text-3xl">Agent 管理器</div>
                      <div>
                        <CreateAgentButton
                          isCanCreateAgent={isCanCreateAgent}
                          onClick={handleCreateAgentBtnClick}
                        />
                      </div>
                    </div>
                    {/* Agent列表，自动换行向下滚动 */}
                    {/*<div className="w-full flex-1 flex flex-wrap justify-start overflow-y-auto">*/}
                    <Flex wrap gap="middle" style={{
                      width: '100%',
                      flex: 1,
                      overflowY: 'auto',
                      scrollbarColor: '#888 transparent',
                      scrollbarWidth: 'thin'
                    }}>
                      {agentList.map(item => (
                          <div
                              onClick={() => handleAgentManagerAgentClick(item)}
                              className=" w-[200px] h-[175px] bg-gray-100 rounded-2xl p-4 flex flex-col cursor-pointer"
                          >
                            <div className="mb-4 flex justify-between">
                              <div>
                                <Avatar
                                  size="small"
                                  style={{
                                    backgroundColor: item.iconColor,
                                    color: item.fontColor,
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                  }}
                                  src={item.icon && (item.icon.startsWith('http://') || item.icon.startsWith('https://'))
                                    ? item.icon
                                    : undefined}
                                >
                                  {item.icon && !(item.icon.startsWith('http://') || item.icon.startsWith('https://')) ? (
                                    <span style={{ fontSize: '14px' }}>{item.icon}</span>
                                  ) : (
                                    !item.icon && (item.name ? item.name[0] : '?')
                                  )}
                                </Avatar>
                              </div>
                              {isCanCreateAgent && (
                                  <div onClick={(event) => {
                                    event.stopPropagation();
                                  }}>
                                    <Dropdown menu={{
                                      items: agentCardMoreItems,
                                      onClick: (info) => handleAgentCardMoreDropDownClick(info, item)
                                    }}
                                              trigger={['click']}
                                    >
                                      <MoreOutlined />
                                    </Dropdown>
                                  </div>
                              )}
                              {!isCanCreateAgent && (
                                  <div onClick={(event) => {
                                    event.stopPropagation();
                                  }}>
                                    <Popover
                                        content="添加至列表"
                                    >
                                      <Button
                                          type={'text'}
                                          size={'small'}
                                          disabled={item.userAgentId != null}
                                          onClick={() => handleAddToList(item)}>
                                        <PlusOutlined />
                                      </Button>
                                    </Popover>
                                  </div>
                              )}
                            </div>
                            <div className="truncate overflow-hidden mb-1 text-sm">{item.name}</div>
                            <div className="overflow-y-hidden text-xs h-[66px]" style={{
                              color: 'rgb(175, 177, 196)',
                            }}>{item.description}</div>
                          </div>
                      ))}
                    </Flex>

                    {/*</div>*/}
                  </div>
              )}
            </div>
          </Content>
        </Layout>
      </Layout>
      <Spin spinning={isShowSpin} tip={'正在处理...'} fullscreen />
      <Modal
        title={renameModalType === 'rename' ? '重命名此对话' : '固定此对话'}
        open={isShowRenameModal}
        onOk={handleConfirmRename}
        onCancel={handleCancelRename}
        okText="保存"
        cancelText="取消"
      >
        <Form
            form={chatNameForm}
            size={'large'}
        >
          <Form.Item
            name="name"
          >
            <Input placeholder="请输入对话名称" />
          </Form.Item>
        </Form>
      </Modal>
      <AgentFormModal
        isOpen={isOpenAgentFormModal}
        onClose={() => {
          // 关闭弹窗时清空 agentFormData
          console.log('弹窗关闭前，当前 agentFormData:', agentFormData);
          setAgentFormData(null);
          setIsOpenAgentFormModal(false);
          console.log('弹窗关闭，已清空 agentFormData');
        }}
        agentFormData={agentFormData}
        onSubmit={() => {
          // 提交成功后关闭弹窗并刷新列表
          console.log('提交成功前，当前 agentFormData:', agentFormData);
          setAgentFormData(null);
          setIsOpenAgentFormModal(false);
          console.log('提交成功，已清空 agentFormData');
          fetchAgentList();
        }}
        onEdit={(_) => {
          // 编辑成功后关闭弹窗并刷新列表
          console.log('编辑成功前，当前 agentFormData:', agentFormData);
          setAgentFormData(null);
          setIsOpenAgentFormModal(false);
          console.log('编辑成功，已清空 agentFormData');
          fetchAgentList();
        }}
        isCanCreateAgent={isCanCreateAgent}
      />

      <VisibilitySelector
        agentDictId={visibleSelectAgentDictId}
        open={isShowVisibleSelectorDialog}
        onClose={() => setIsShowVisibleSelectorDialog(false)}
        onSuccess={() => {
          // 可见性设置成功后的回调
          // 例如刷新列表等
        }}
      />
    </>
  );
};



export default Independent;
