import React from 'react';
import { <PERSON><PERSON>, Button, Dropdown, Flex, Popover } from 'antd';
import { MoreOutlined, PlusOutlined } from '@ant-design/icons';
import { AgentDict } from '../../../types/agentDict';
import { UserAgentList } from '../../../types/userAgentList';
import { CreateAgentButton } from './buttons';
import AgentFormModal from '../../../components/agentManager/agentFormModal';
import VisibilitySelector from '../../../components/AgentDictVisibility';
import { agentCardMoreItems } from '../constants';

interface AgentManagerProps {
  // Agent列表数据
  agentList: AgentDict[];
  
  // 权限控制
  isCanCreateAgent: boolean;
  
  // 模态框状态
  isOpenAgentFormModal: boolean;
  agentFormData: AgentDict | null;
  isShowVisibleSelectorDialog: boolean;
  visibleSelectAgentDictId: string;
  
  // 事件处理函数
  onAgentClick: (agent: AgentDict) => void;
  onCreateAgent: () => void;
  onAgentCardMoreClick: (info: any, item: AgentDict) => void;
  onAddToList: (agent: AgentDict) => Promise<void>;
  
  // 模态框事件
  onAgentFormModalClose: () => void;
  onAgentFormSubmit: () => void;
  onAgentFormEdit: (agent: AgentDict) => void;
  onVisibleSelectorClose: () => void;
  onVisibleSelectorSuccess: () => void;
  
  // 刷新列表
  onRefreshAgentList: () => void;
}

const AgentManager: React.FC<AgentManagerProps> = ({
  agentList,
  isCanCreateAgent,
  isOpenAgentFormModal,
  agentFormData,
  isShowVisibleSelectorDialog,
  visibleSelectAgentDictId,
  onAgentClick,
  onCreateAgent,
  onAgentCardMoreClick,
  onAddToList,
  onAgentFormModalClose,
  onAgentFormSubmit,
  onAgentFormEdit,
  onVisibleSelectorClose,
  onVisibleSelectorSuccess,
  onRefreshAgentList
}) => {
  const handleAgentClick = (agent: AgentDict) => {
    onAgentClick(agent);
  };

  const handleAddToList = async (agent: AgentDict) => {
    await onAddToList(agent);
  };

  return (
    <>
      <div className="w-full h-full flex flex-col">
        {/* 标题加按钮，按钮有权限控制 */}
        <div className="w-full h-[100px] flex flex-row items-center justify-between mb-4">
          <div className="text-3xl">Agent 管理器</div>
          <div>
            <CreateAgentButton
              isCanCreateAgent={isCanCreateAgent}
              onClick={onCreateAgent}
            />
          </div>
        </div>
        
        {/* Agent列表，自动换行向下滚动 */}
        <Flex wrap gap="middle" style={{
          width: '100%',
          flex: 1,
          overflowY: 'auto',
          scrollbarColor: '#888 transparent',
          scrollbarWidth: 'thin'
        }}>
          {agentList.map(item => (
            <div
              key={item.id}
              onClick={() => handleAgentClick(item)}
              className="w-[200px] h-[175px] bg-gray-100 rounded-2xl p-4 flex flex-col cursor-pointer"
            >
              <div className="mb-4 flex justify-between">
                <div>
                  <Avatar
                    size="small"
                    style={{
                      backgroundColor: item.iconColor,
                      color: item.fontColor,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}
                    src={item.icon && (item.icon.startsWith('http://') || item.icon.startsWith('https://'))
                      ? item.icon
                      : undefined}
                  >
                    {item.icon && !(item.icon.startsWith('http://') || item.icon.startsWith('https://')) ? (
                      <span style={{ fontSize: '14px' }}>{item.icon}</span>
                    ) : (
                      !item.icon && (item.name ? item.name[0] : '?')
                    )}
                  </Avatar>
                </div>
                {isCanCreateAgent && (
                  <div onClick={(event) => {
                    event.stopPropagation();
                  }}>
                    <Dropdown 
                      menu={{
                        items: agentCardMoreItems,
                        onClick: (info) => onAgentCardMoreClick(info, item)
                      }}
                      trigger={['click']}
                    >
                      <MoreOutlined />
                    </Dropdown>
                  </div>
                )}
                {!isCanCreateAgent && (
                  <div onClick={(event) => {
                    event.stopPropagation();
                  }}>
                    <Popover content="添加至列表">
                      <Button
                        type={'text'}
                        size={'small'}
                        disabled={item.userAgentId != null}
                        onClick={() => handleAddToList(item)}
                      >
                        <PlusOutlined />
                      </Button>
                    </Popover>
                  </div>
                )}
              </div>
              <div className="truncate overflow-hidden mb-1 text-sm">{item.name}</div>
              <div className="overflow-y-hidden text-xs h-[66px]" style={{
                color: 'rgb(175, 177, 196)',
              }}>
                {item.description}
              </div>
            </div>
          ))}
        </Flex>
      </div>

      {/* Agent表单模态框 */}
      <AgentFormModal
        isOpen={isOpenAgentFormModal}
        onClose={onAgentFormModalClose}
        agentFormData={agentFormData}
        onSubmit={() => {
          onAgentFormSubmit();
          onRefreshAgentList();
        }}
        onEdit={(agent) => {
          onAgentFormEdit(agent);
          onRefreshAgentList();
        }}
        isCanCreateAgent={isCanCreateAgent}
      />

      {/* 可见性选择器 */}
      <VisibilitySelector
        agentDictId={visibleSelectAgentDictId}
        open={isShowVisibleSelectorDialog}
        onClose={onVisibleSelectorClose}
        onSuccess={onVisibleSelectorSuccess}
      />
    </>
  );
};

export default AgentManager;
