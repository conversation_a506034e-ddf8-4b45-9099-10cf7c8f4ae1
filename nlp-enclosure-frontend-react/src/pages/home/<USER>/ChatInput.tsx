import React from 'react';
import { Sender, Attachments } from '@ant-design/x';
import { CloudUploadOutlined } from '@ant-design/icons';
import { GetProp } from 'antd';
import { AgentDict } from '../../../types/agentDict';
import { AgentParam } from '../../../types/agentParam';
import SenderFooter from './SenderFooter';
import styles from '../index.module.css';

interface ChatInputProps {
  // 输入内容相关
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;

  // 提交和取消
  onSubmit: (content: string, agent?: AgentDict, parentMessageId?: string) => void;
  onCancel: () => void;

  // 状态
  loading: boolean;
  disabled: boolean;

  // 文件上传相关
  attachedFiles: any[];
  headerOpen: boolean;
  onHeaderOpenChange: (open: boolean) => void;
  onFileChange: GetProp<typeof Attachments, 'onChange'>;
  onFileBeforeUpload: (file: any) => boolean | string | undefined;
  onFileUpload: (file: any) => Promise<void>;

  // 语音录制相关
  isSpeechRecording: boolean;
  onSpeechRecordingChange: (recording: boolean) => void;

  // Agent相关
  currentAgent: AgentDict | null;
  currentAgentParam: AgentParam | null;

  // Footer相关
  defaultAgentIsOpenThink: boolean;
  isDisableSwitchOpenThink: boolean;
  hasMessages: boolean;
  realtimePlayEnabled: boolean;
  onDeepThinkToggle: () => void;
  onRealtimePlayToggle: () => void;

  // 样式
  className?: string;
}

const ChatInput: React.FC<ChatInputProps> = ({
  content,
  onChange,
  placeholder = "有什么我能帮您的吗？",
  onSubmit,
  onCancel,
  loading,
  disabled,
  attachedFiles,
  headerOpen,
  onHeaderOpenChange,
  onFileChange,
  onFileBeforeUpload,
  onFileUpload,
  isSpeechRecording,
  onSpeechRecordingChange,
  currentAgent,
  currentAgentParam,
  defaultAgentIsOpenThink,
  isDisableSwitchOpenThink,
  hasMessages,
  realtimePlayEnabled,
  onDeepThinkToggle,
  onRealtimePlayToggle,
  className
}) => {
  const senderHeader = (
    <Sender.Header
      title="附件上传"
      open={headerOpen}
      onOpenChange={onHeaderOpenChange}
      styles={{
        content: {
          padding: 0
        }
      }}
    >
      <Attachments
        beforeUpload={onFileBeforeUpload}
        items={attachedFiles}
        onChange={onFileChange}
        customRequest={onFileUpload}
        maxCount={8}
        placeholder={(type) =>
          type === 'drop'
            ? { title: '拖动文件到此处' }
            : {
                icon: <CloudUploadOutlined />,
                title: '附件上传',
                description: '单击或拖动文件到此区域上传附件'
              }
        }
        disabled={attachedFiles.length >= 8}
      />
    </Sender.Header>
  );

  const senderFooter = (
    <SenderFooter
      currentAgentParam={currentAgentParam}
      headerOpen={headerOpen}
      attachedFilesCount={attachedFiles.length}
      defaultAgentIsOpenThink={defaultAgentIsOpenThink}
      isDisableSwitchOpenThink={isDisableSwitchOpenThink}
      hasMessages={hasMessages}
      realtimePlayEnabled={realtimePlayEnabled}
      onFileUploadToggle={() => onHeaderOpenChange(!headerOpen)}
      onDeepThinkToggle={onDeepThinkToggle}
      onRealtimePlayToggle={onRealtimePlayToggle}
    />
  );

  return (
    <Sender
      value={content}
      placeholder={placeholder}
      header={senderHeader}
      onSubmit={onSubmit}
      onCancel={onCancel}
      onChange={onChange}
      loading={loading}
      className={className}
      disabled={disabled}
      classNames={{
        input: "sender-input"
      }}
      footer={senderFooter}
      allowSpeech={{
        recording: isSpeechRecording,
        onRecordingChange: onSpeechRecordingChange
      }}
    />
  );
};

export default ChatInput;
